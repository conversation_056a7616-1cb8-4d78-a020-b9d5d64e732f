{"dependencies": {"@create-figma-plugin/ui": "^4.0.3", "@create-figma-plugin/utilities": "^4.0.3", "preact": ">=10"}, "devDependencies": {"@create-figma-plugin/build": "^4.0.3", "@create-figma-plugin/tsconfig": "^4.0.3", "@figma/plugin-typings": "1.109.0", "@types/bun": "latest", "@types/node": "^24.3.1", "typescript": ">=5"}, "scripts": {"build": "build-figma-plugin --typecheck --minify", "watch": "build-figma-plugin --typecheck --watch", "test": "bun test"}, "figma-plugin": {"editorType": ["figma"], "id": "find-replace", "name": "Find & Replace", "main": "src/main.ts", "ui": "src/ui.tsx"}, "name": "figma-plugin--find-replace", "private": true}