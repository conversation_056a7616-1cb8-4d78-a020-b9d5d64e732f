import type { <PERSON>Handler } from "@create-figma-plugin/utilities";

// Re-export Channel class and utilities for easy access
export { Channel, createChannel } from "./lib/channel";

/**
 * Event handler for creating rectangles
 */
export interface CreateRectanglesHandler extends EventHandler {
	name: "CREATE_RECTANGLES";
	handler: (count: number) => void;
}

/**
 * Event handler for closing the plugin
 */
export interface CloseHandler extends EventHandler {
	name: "CLOSE";
	handler: () => void;
}

/**
 * Union type of all event handlers used in the plugin
 * This helps with type safety when using the Channel class
 */
export type PluginEventHandler = CreateRectanglesHandler | CloseHandler;

/**
 * Type for event names used in the plugin
 */
export type PluginEventName = PluginEventHandler["name"];

/**
 * Type helper to extract handler function type from event name
 */
export type HandlerForEvent<T extends PluginEventName> = Extract<
	PluginEventHandler,
	{ name: T }
>["handler"];
