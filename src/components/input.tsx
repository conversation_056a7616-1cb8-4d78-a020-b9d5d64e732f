import {
	Checkbox as _Checkbox,
	Mu<PERSON>,
	Stack,
	Text,
	Textbox,
} from "@create-figma-plugin/ui";
import { h, type JSX } from "preact";

/**
 * Renders a labeled input field component with data attribute support.
 *
 * @param {Object} props - The component props
 * @param {string} props.label - The label text to display above the input
 * @param {string} props.value - The current value of the input field
 * @param {function} props.onChange - Callback function triggered when input value changes
 * @param {Object} props.dataAttributes - Additional data attributes to be applied to the input
 * @returns {JSX.Element} A stack containing a labeled input field
 */
export function Input({
	label,
	value,
	onChange,
	...dataAttributes
}: InputProps): JSX.Element {
	return (
		<Stack space="extraSmall">
			<Text>
				<Muted>{label}</Muted>
			</Text>
			<Textbox
				value={value}
				onInput={onChange}
				{...getDataAttributes(dataAttributes)}
			/>
		</Stack>
	);
}

type InputProps = InputBaseProps & {
	value: string;
};

type InputBaseProps = DataAttributes & {
	label: string;
	onChange(e: JSX.TargetedEvent<HTMLInputElement>): void;
};

type DataAttributes = { [key: `data-${string}`]: string };

/**
 * Renders a labeled checkbox component with data attribute support.
 *
 * @param {Object} props - The component props
 * @param {string} props.label - The label text to display next to the checkbox
 * @param {boolean} props.checked - The current checked state of the checkbox
 * @param {function} props.onChange - Callback function triggered when checkbox state changes
 * @param {Object} props.dataAttributes - Additional data attributes to be applied to the checkbox
 * @returns {JSX.Element} A checkbox with a label
 */
export function Checkbox({
	label,
	checked,
	onChange,
	...dataAttributes
}: CheckboxProps): JSX.Element {
	return (
		<_Checkbox
			value={checked}
			onChange={onChange}
			{...getDataAttributes(dataAttributes)}
		>
			<Text>{label}</Text>
		</_Checkbox>
	);
}

type CheckboxProps = InputBaseProps & {
	checked: boolean;
};

/**
 * Extracts data attributes from the given props object.
 *
 * @param {Object} props - The component props
 * @returns {Object} An object containing only the data attributes
 */
function getDataAttributes<T extends object>(props: T): DataAttributes {
	return Object.fromEntries(
		Object.entries(props).filter(([key, _]) => key.startsWith("data-")),
	);
}
