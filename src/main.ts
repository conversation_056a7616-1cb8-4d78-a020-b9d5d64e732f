import { showUI } from "@create-figma-plugin/utilities";

import type { <PERSON><PERSON><PERSON><PERSON>, CreateRectanglesHandler } from "./types";
import { createChannel } from "./types";

export default function () {
	// Create a channel for communication with the UI
	const channel = createChannel();

	// Set up event handlers using the channel
	channel.once<CreateRectanglesHandler>(
		"CREATE_RECTANGLES",
		(count: number) => {
			const nodes: Array<SceneNode> = [];
			for (let i = 0; i < count; i++) {
				const rect = figma.createRectangle();
				rect.x = i * 150;
				rect.fills = [
					{
						color: { b: 0, g: 0.5, r: 1 },
						type: "SOLID",
					},
				];
				figma.currentPage.appendChild(rect);
				nodes.push(rect);
			}
			figma.currentPage.selection = nodes;
			figma.viewport.scrollAndZoomIntoView(nodes);
			figma.closePlugin();
		},
	);

	channel.once<CloseHandler>("CLOSE", () => {
		figma.closePlugin();
	});

	// Show the UI
	showUI({
		height: 124,
		width: 240,
	});
}
