import { type <PERSON><PERSON><PERSON><PERSON> } from "@create-figma-plugin/utilities";
import { Channel } from "./channel";
import { Context } from "./context";
import { sleep, truncate } from "./util";

/**
 * Recursively search for nodes of a specific type within a selection.
 *
 * @param selection - The selection of nodes to search within.
 * @param type - The type of node to search for.
 * @param includeHidden - Whether to include hidden nodes in the search.
 * @param context - Optional context to cancel the search.
 * @param channel - Optional channel to send status updates.
 * @returns {Promise<T[]>} A promise that resolves to an array of nodes of the specified type.
 */
export async function GetNodesOfTypeRecursive<T extends SceneNode>(
	selection: readonly SceneNode[],
	type: T["type"],
	includeHidden: boolean = false,
	context?: Context,
	channel?: Channel<GetNodesOfTypeRecursiveStatusHandler>,
): Promise<T[]> {
	const ctx = context ?? new Context();
	const result: T[] = [];

	for (const node of selection) {
		if (ctx.done) {
			return [];
		}

		if (!includeHidden && !node.visible) {
			continue;
		}

		channel?.send("GET_NODES_OF_TYPE_RECURSIVE_STATUS", truncate(node.name, 32));

		if (node.type === type) {
			result.push(node as T);
			continue;
		}

		if ("children" in node) {
			result.push(
				...(await GetNodesOfTypeRecursive(
					node.children,
					type,
					includeHidden,
					ctx,
					channel,
				)),
			);
		}

		await sleep(0);
	}

	return result;
}

export interface GetNodesOfTypeRecursiveStatusHandler extends EventHandler {
	name: "GET_NODES_OF_TYPE_RECURSIVE_STATUS";
	handler: (status: string) => void;
}