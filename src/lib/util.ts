/**
 * Sleep for the specified number of milliseconds.
 *
 * @export
 * @param {number} ms - The number of milliseconds to sleep.
 * @returns {Promise<void>} A promise that resolves when the sleep is complete.
 */
export function sleep(ms: number): Promise<void> {
	return new Promise<void>((resolve) => setTimeout(resolve, ms));
}

/**
 * Truncate a string to the specified length.
 *
 * @export
 * @param {string} str - The string to truncate.
 * @param {number} len - The maximum length of the string.
 * @returns {string} The truncated string.
 */
export function truncate(str: string, len: number): string {
	return str.length > len ? `${str.slice(0, len - 3)}...` : str;
}
