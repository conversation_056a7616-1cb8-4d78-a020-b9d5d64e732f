import {
	Button,
	Columns,
	Container,
	Muted,
	render,
	Text,
	TextboxNumeric,
	VerticalSpace,
} from "@create-figma-plugin/ui";
import { h } from "preact";
import { useCallback, useMemo, useState } from "preact/hooks";

import type { <PERSON><PERSON><PERSON><PERSON>, <PERSON>reate<PERSON><PERSON>tangles<PERSON>and<PERSON> } from "./types";
import { createChannel } from "./types";

function Plugin() {
	const [count, setCount] = useState<number | null>(5);
	const [countString, setCountString] = useState("5");

	// Create a channel instance for communication with the main thread
	const channel = useMemo(() => createChannel(), []);

	const handleCreateRectanglesButtonClick = useCallback(() => {
		if (count !== null) {
			channel.send<CreateRectanglesHandler>("CREATE_RECTANGLES", count);
		}
	}, [count, channel]);

	const handleCloseButtonClick = useCallback(() => {
		channel.send<CloseHandler>("CLOSE");
	}, [channel]);

	return (
		<Container space="medium">
			<VerticalSpace space="large" />
			<Text>
				<Muted>Count</Muted>
			</Text>
			<VerticalSpace space="small" />
			<TextboxNumeric
				onNumericValueInput={setCount}
				onValueInput={setCountString}
				value={countString}
			/>
			<VerticalSpace space="extraLarge" />
			<Columns space="extraSmall">
				<Button fullWidth onClick={handleCreateRectanglesButtonClick}>
					Create
				</Button>
				<Button fullWidth onClick={handleCloseButtonClick} secondary>
					Close
				</Button>
			</Columns>
			<VerticalSpace space="small" />
		</Container>
	);
}

export default render(Plugin);
